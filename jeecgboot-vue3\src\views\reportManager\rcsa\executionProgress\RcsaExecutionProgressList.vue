<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item name="evaluateEndDate">
              <template #label><span title="评估期间">评估期间</span></template>
              <a-range-picker value-format="YYYY-MM-DD" v-model:value="queryParam.evaluateEndDate" class="query-group-cust" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="planType">
              <template #label><span title="计划类型">计划类型</span></template>
              <j-select-multiple placeholder="请选择计划类型" v-model:value="queryParam.planType" dictCode="rcsa_plan_type" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="evaluateDepart">
              <template #label><span title="评估部门">评估部门</span></template>
              <j-select-dept placeholder="请选择评估部门" v-model:value="queryParam.evaluateDepart" multiple allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <span style="float: right; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="default" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 12px">重置</a-button>
              <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery" style="margin-left: 12px">查询</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :title="tableTitle">
      <template #toolbar>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="onExportXls"> 导出</a-button>
      </template>
    </BasicTable>

    <!-- 明细数据弹窗 -->
    <RcsaExecutionProgressDetailModal ref="detailModal" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, DRILL_DOWN_COLORS, DRILLABLE_COLUMNS, DRILL_DOWN_DISPLAY_NAMES } from './RcsaExecutionProgress.data';
  import { h } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import { queryExecutionProgress, getExportUrl } from './RcsaExecutionProgress.api';
  import RcsaExecutionProgressDetailModal from './components/RcsaExecutionProgressDetailModal.vue';
  import JSelectMultiple from '/@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JSelectDept from '/@/components/Form/src/jeecg/components/JSelectDept.vue';
  const formRef = ref();
  const detailModal = ref();
  const queryParam = reactive<any>({
    column: 'evaluateDepart',
    order: 'desc',
  });

  // 表格标题 - 当前日期
  const tableTitle = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日 RCSA评估执行进度表`;
  });

  // 创建带钻取功能的列配置
  const drillDownColumns = columns.map((col) => {
    if (DRILLABLE_COLUMNS.includes(col.dataIndex as any)) {
      return {
        ...col,
        customRender: ({ text, record }: any) => {
          const color = DRILL_DOWN_COLORS[col.dataIndex as keyof typeof DRILL_DOWN_COLORS] || '#1890ff';
          const displayName = DRILL_DOWN_DISPLAY_NAMES[col.dataIndex as keyof typeof DRILL_DOWN_DISPLAY_NAMES] || col.title;

          return h(
            'a',
            {
              style: {
                color,
                cursor: 'pointer',
                textDecoration: 'underline',
                fontWeight: '500',
              },
              title: `点击查看${displayName}明细`,
              onClick: () => handleDrillDown(record, col.dataIndex as string),
            },
            text
          );
        },
      };
    }
    return col;
  });

  /**
   * 钻取处理函数
   */
  function handleDrillDown(record: any, type: string) {
    console.log('钻取数据:', record, '类型:', type);

    // 确保弹窗组件已经加载
    if (detailModal.value && detailModal.value.handleOpen) {
      detailModal.value.handleOpen({
        ...record,
        drillDownType: type, // 传递钻取类型，用于后续可能的筛选
      });
    } else {
      console.error('弹窗组件未正确加载');
    }
  }

  //注册table数据
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      api: queryExecutionProgress,
      columns: drillDownColumns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      defSort: {
        column: 'evaluateDepart',
        order: 'desc',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        let rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
      },
    },
    exportConfig: {
      name: '评估执行进度表',
      url: getExportUrl,
      params: queryParam,
    },
  });
  const [registerTable, { reload }] = tableContext;

  let rangeField = 'evaluateEndDate';

  /**
   * 设置范围查询条件
   */
  async function setRangeQuery() {
    let queryParamClone = cloneDeep(queryParam);
    if (rangeField) {
      let fieldsValue = rangeField.split(',');
      fieldsValue.forEach((item) => {
        if (queryParamClone[item]) {
          let range = queryParamClone[item];
          queryParamClone[item + '_begin'] = range[0];
          queryParamClone[item + '_end'] = range[1];
          delete queryParamClone[item];
        } else {
          queryParamClone[item + '_begin'] = '';
          queryParamClone[item + '_end'] = '';
        }
      });
    }
    return queryParamClone;
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    //刷新数据
    reload();
  }

  onMounted(() => {
    searchQuery();
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }

    .ant-form-item:not(.ant-form-item-with-help) {
      margin-bottom: 16px;
      height: 32px;
    }

    :deep(.ant-picker),
    :deep(.ant-input-number) {
      width: 100%;
    }
  }
</style>
