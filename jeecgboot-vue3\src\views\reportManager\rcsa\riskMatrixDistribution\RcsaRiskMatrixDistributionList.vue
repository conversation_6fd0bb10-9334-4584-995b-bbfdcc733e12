<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <BasicForm @register="registerForm" @submit="searchQuery" @reset="searchReset" />
    </div>

    <!--图表区域-->
    <div class="mt-4">
      <a-card :title="tableTitle+'图'" :bordered="false">
        <div ref="chartRef" style="height: 400px; width: 100%"></div>
      </a-card>
    </div>

    <!--表格区域-->
    <div class="mt-4">
      <BasicTable @register="registerTable" :title="tableTitle+'表'">
        <!--操作栏-->
        <template #toolbar>
          <a-button type="primary" preIcon="ant-design:download-outlined" @click="onExportXls"> 导出</a-button>
        </template>
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="DRILLABLE_COLUMNS.includes(column.dataIndex)">
            <a @click="handleDrillClick(record, getDrillType(column.dataIndex))" class="drill-link">
              {{ getDrillDisplayValue(record, column.dataIndex) }}
            </a>
          </template>
        </template>
      </BasicTable>
    </div>

    <!-- 钻取详情弹窗 -->
    <RiskMatrixDrillDetailModal @register="registerDrillModal" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, nextTick, onMounted, computed } from "vue";
  import { BasicTable } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import RiskMatrixDrillDetailModal from './components/RiskMatrixDrillDetailModal.vue';
  import { distributionColumns, searchFormSchema, DRILLABLE_COLUMNS } from './RcsaRiskMatrixDistribution.data';
  import { queryRiskMatrixDistribution, getExportUrl } from './RcsaRiskMatrixDistribution.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useECharts } from '/@/hooks/web/useECharts';

  // 表格标题 - 当前日期
  const tableTitle = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日 RCSA风险评估结果矩阵分布`;
  });

  const { createMessage } = useMessage();
  const chartRef = ref<HTMLDivElement | null>(null);
  const queryParams = reactive<any>({});

  // 错误处理
  const handleError = (error: any, message: string) => {
    console.error(message, error);
    createMessage.error(message);
  };

  // 注册表单
  const [registerForm, { getFieldsValue, resetFields }] = useForm({
    labelWidth: 120,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    fieldMapToNumber: [],
    fieldMapToTime: [['dateRange', ['evaluateStartDate', 'evaluateEndDate'], 'YYYY-MM-DD']],
    autoAdvancedLine: 3,
    actionColOptions: {
      span: 8,
    },
  });

  // 注册钻取弹窗
  const [registerDrillModal, { openModal: openDrillModal }] = useModal();

  // 设置查询参数函数
  async function setRangeQuery() {
    let rangeQuery = {};
    const formValues = getFieldsValue();
    Object.assign(rangeQuery, formValues);
    return rangeQuery;
  }

  // 注册表格数据 - 使用useListPage
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      api: queryRiskMatrixDistribution,
      columns: distributionColumns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      defSort: {
        column: 'evaluateDepart',
        order: 'desc',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        const rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
      },
      afterFetch: (dataSource) => {
        try {
          // 数据获取后更新图表
          nextTick(() => {
            updateChart(dataSource);
          });
          return dataSource;
        } catch (error) {
          handleError(error, '处理数据失败');
          return dataSource;
        }
      },
    },
    exportConfig: {
      name: 'RCSA风险评估结果矩阵分布图',
      url: getExportUrl,
      params: queryParams,
    },
  });

  const [registerTable, { reload, setColumns }] = tableContext;

  // 更新列显示状态
  function updateColumnVisibility() {
    const formValues = getFieldsValue();
    const statisticDimension = formValues.statisticDimension || 'department';

    const updatedColumns = distributionColumns.map((col) => {
      if (col.dataIndex === 'matrixName') {
        return { ...col, ifShow: statisticDimension === 'matrix' };
      }
      return col;
    });

    setColumns(updatedColumns);
  }

  // 查询
  function searchQuery() {
    const values = getFieldsValue();
    Object.assign(queryParams, values);
    updateColumnVisibility();
    reload();
  }

  // 重置
  function searchReset() {
    Object.keys(queryParams).forEach((key) => {
      delete queryParams[key];
    });
    updateColumnVisibility();
    reload();
  }

  // 处理钻取点击事件
  function handleDrillClick(record: any, drillType: string) {
    console.log('钻取点击:', record, drillType);

    const formValues = getFieldsValue();
    const drillParams = {
      evaluateDepart: record.evaluateDepart,
      evaluateDepart_dictText: record.evaluateDepart_dictText,
      matrixName: record.matrixName,
      statisticDimension: formValues.statisticDimension || 'department',
      drillType: drillType,
      evaluateStartDate: formValues.evaluateStartDate,
      evaluateEndDate: formValues.evaluateEndDate,
      planTypes: formValues.planTypes,
      institutions: formValues.institutions,
      departments: formValues.departments,
      adjustedRemainRiskLevels: formValues.adjustedRemainRiskLevels,
    };

    openDrillModal(true, drillParams);
  }

  // 获取钻取类型
  function getDrillType(dataIndex: string): string {
    const typeMap = {
      riskTotalCount: 'riskTotal',
      veryHighRiskCount: 'veryHigh',
      veryHighRiskRatio: 'veryHigh',
      highRiskCount: 'high',
      highRiskRatio: 'high',
      mediumRiskCount: 'medium',
      mediumRiskRatio: 'medium',
      lowRiskCount: 'low',
      lowRiskRatio: 'low',
      minorRiskCount: 'minor',
      minorRiskRatio: 'minor',
    };
    return typeMap[dataIndex] || 'riskTotal';
  }

  // 获取钻取显示值
  function getDrillDisplayValue(record: any, dataIndex: string): string {
    const value = record[dataIndex];
    if (dataIndex.includes('Ratio')) {
      return `${value}%`;
    }
    return value;
  }

  // 初始化图表
  const { setOptions } = useECharts(chartRef as any);

  // 更新图表
  function updateChart(dataSource: any[]) {
    if (!dataSource || dataSource.length === 0) {
      return;
    }
    // 逆转
    let dataList: any[] = [];
    dataSource.forEach((row) => {
      dataList.push(row);
    });
    dataList.reverse();

    const formValues = getFieldsValue();
    const statisticDimension = formValues.statisticDimension || 'department';

    // 准备图表数据
    const categories = dataList.map((item) =>
      statisticDimension === 'matrix'
        ? `${item.evaluateDepart_dictText || item.evaluateDepart}-${item.matrixName}`
        : item.evaluateDepart_dictText || item.evaluateDepart
    );

    const seriesData = [
      {
        name: '很高风险',
        type: 'bar',
        stack: 'total',
        data: dataList.map((item) => item.veryHighRiskCount || 0),
        itemStyle: { color: '#ff4d4f' },
      },
      {
        name: '高风险',
        type: 'bar',
        stack: 'total',
        data: dataList.map((item) => item.highRiskCount || 0),
        itemStyle: { color: '#ff7a45' },
      },
      {
        name: '中风险',
        type: 'bar',
        stack: 'total',
        data: dataList.map((item) => item.mediumRiskCount || 0),
        itemStyle: { color: '#ffa940' },
      },
      {
        name: '低风险',
        type: 'bar',
        stack: 'total',
        data: dataList.map((item) => item.lowRiskCount || 0),
        itemStyle: { color: '#52c41a' },
      },
      {
        name: '轻微风险',
        type: 'bar',
        stack: 'total',
        data: dataList.map((item) => item.minorRiskCount || 0),
        itemStyle: { color: '#1890ff' },
      },
    ];

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        top: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      series: seriesData,
    };

    setOptions(option);
  }

  // 组件初始化
  onMounted(() => {
    try {
      // 初始化列显示状态
      updateColumnVisibility();
    } catch (error) {
      handleError(error, '组件初始化失败');
    }
  });
</script>

<style scoped>
  :deep(.drill-link) {
    color: #1890ff;
    cursor: pointer;
  }

  :deep(.drill-link:hover) {
    color: #40a9ff;
    text-decoration: underline;
  }
</style>
