package com.gientech.rcsa.report;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.service.IRcsaMatrixStatisticsService;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计测试类
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@SpringBootTest
@Slf4j
public class RcsaMatrixStatisticsTest {

    @Autowired
    private IRcsaMatrixStatisticsService rcsaMatrixStatisticsService;

    @Test
    public void testQueryMatrixStatisticsByDepartmentWithPagination() {
        try {
            // 创建查询条件 - 部门维度
            RcsaMatrixStatisticsDto queryDto = new RcsaMatrixStatisticsDto();
            queryDto.setEvaluateStartDate(LocalDate.of(2024, 1, 1));
            queryDto.setEvaluateEndDate(LocalDate.of(2024, 12, 31));
            queryDto.setPlanTypes(Arrays.asList("1", "2")); // 假设的计划类型
            queryDto.setStatisticDimension("department");
            
            // 创建分页参数
            Page<RcsaMatrixStatisticsVo> page = new Page<>(1, 10);
            
            // 执行分页查询
            IPage<RcsaMatrixStatisticsVo> resultPage = rcsaMatrixStatisticsService.queryMatrixStatistics(page, queryDto);
            
            // 验证结果
            log.info("部门维度分页查询结果: 总记录数={}, 当前页记录数={}", resultPage.getTotal(), resultPage.getRecords().size());
            
            // 基本断言
            assert resultPage != null;
            assert resultPage.getRecords() != null;
            
            for (RcsaMatrixStatisticsVo result : resultPage.getRecords()) {
                assert result.getRiskTotalCount() != null;
                assert result.getControlTotalCount() != null;
                assert result.getIndicatorTotalCount() != null;
                assert result.getEventTotalCount() != null;
            }
            
            log.info("测试通过：部门维度RCSA矩阵关联要素统计分页查询功能正常");
            
        } catch (Exception e) {
            log.error("部门维度分页测试失败", e);
            throw e;
        }
    }

    @Test
    public void testQueryAllMatrixStatisticsForExport() {
        try {
            // 创建查询条件 - 矩阵维度
            RcsaMatrixStatisticsDto queryDto = new RcsaMatrixStatisticsDto();
            queryDto.setEvaluateStartDate(LocalDate.of(2024, 1, 1));
            queryDto.setEvaluateEndDate(LocalDate.of(2024, 12, 31));
            queryDto.setStatisticDimension("matrix");
            
            // 执行查询所有数据（用于导出）
            List<RcsaMatrixStatisticsVo> resultList = rcsaMatrixStatisticsService.queryAllMatrixStatistics(queryDto);
            
            // 验证结果
            log.info("矩阵维度导出查询结果: 总记录数={}", resultList.size());
            
            // 基本断言
            assert resultList != null;
            
            log.info("测试通过：矩阵维度RCSA矩阵关联要素统计导出查询功能正常");
            
        } catch (Exception e) {
            log.error("矩阵维度导出测试失败", e);
            throw e;
        }
    }

    @Test
    public void testQueryMatrixStatisticsWithEmptyCondition() {
        try {
            // 创建空查询条件
            RcsaMatrixStatisticsDto queryDto = new RcsaMatrixStatisticsDto();
            
            // 创建分页参数
            Page<RcsaMatrixStatisticsVo> page = new Page<>(1, 10);
            
            // 执行分页查询
            IPage<RcsaMatrixStatisticsVo> resultPage = rcsaMatrixStatisticsService.queryMatrixStatistics(page, queryDto);
            
            // 验证结果
            log.info("空条件分页查询结果: 总记录数={}, 当前页记录数={}", resultPage.getTotal(), resultPage.getRecords().size());
            
            // 基本断言
            assert resultPage != null;
            
            log.info("测试通过：空条件分页查询功能正常");
            
        } catch (Exception e) {
            log.error("空条件分页测试失败", e);
            throw e;
        }
    }
}
