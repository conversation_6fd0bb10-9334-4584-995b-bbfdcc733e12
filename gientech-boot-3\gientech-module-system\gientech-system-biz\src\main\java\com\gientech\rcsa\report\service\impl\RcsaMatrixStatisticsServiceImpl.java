package com.gientech.rcsa.report.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.mapper.RcsaMatrixStatisticsMapper;
import com.gientech.rcsa.report.service.IRcsaMatrixStatisticsService;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Service实现类
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Service
@Slf4j
public class RcsaMatrixStatisticsServiceImpl implements IRcsaMatrixStatisticsService {

    @Autowired
    private RcsaMatrixStatisticsMapper rcsaMatrixStatisticsMapper;

    @Override
    public IPage<RcsaMatrixStatisticsVo> queryMatrixStatistics(Page<RcsaMatrixStatisticsVo> page, RcsaMatrixStatisticsDto queryDto) {
        log.info("分页查询RCSA矩阵关联要素统计数据，查询条件：{}，分页参数：{}", queryDto, page);

        try {
            // 设置默认统计维度
            if (!StringUtils.hasText(queryDto.getStatisticDimension())) {
                queryDto.setStatisticDimension("department");
            }

            // 分页查询统计数据
            IPage<RcsaMatrixStatisticsVo> resultPage = rcsaMatrixStatisticsMapper.queryMatrixStatistics(page, queryDto);

            if (resultPage.getRecords() != null && !resultPage.getRecords().isEmpty()) {
                // 处理查询结果
                for (RcsaMatrixStatisticsVo result : resultPage.getRecords()) {
                    processStatisticsResult(result);
                }
            }

            log.info("RCSA矩阵关联要素统计分页查询完成，返回{}条记录", resultPage.getRecords().size());
            return resultPage;

        } catch (Exception e) {
            log.error("分页查询RCSA矩阵关联要素统计数据失败", e);
            throw new RuntimeException("查询统计数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<RcsaMatrixStatisticsVo> queryAllMatrixStatistics(RcsaMatrixStatisticsDto queryDto) {
        log.info("查询所有RCSA矩阵关联要素统计数据（用于导出），查询条件：{}", queryDto);

        try {
            // 设置默认统计维度
            if (!StringUtils.hasText(queryDto.getStatisticDimension())) {
                queryDto.setStatisticDimension("department");
            }

            // 查询所有统计数据
            List<RcsaMatrixStatisticsVo> resultList = rcsaMatrixStatisticsMapper.queryAllMatrixStatistics(queryDto);

            if (resultList == null || resultList.isEmpty()) {
                resultList = new ArrayList<>();
                // 返回一条默认数据
                RcsaMatrixStatisticsVo defaultResult = createDefaultStatistics(queryDto.getStatisticDimension());
                resultList.add(defaultResult);
            } else {
                // 处理查询结果
                for (RcsaMatrixStatisticsVo result : resultList) {
                    processStatisticsResult(result);
                }
            }

            log.info("RCSA矩阵关联要素统计查询完成，返回{}条记录", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("查询所有RCSA矩阵关联要素统计数据失败", e);
            throw new RuntimeException("查询统计数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建默认统计数据
     */
    private RcsaMatrixStatisticsVo createDefaultStatistics(String statisticDimension) {
        RcsaMatrixStatisticsVo result = new RcsaMatrixStatisticsVo();

        if ("matrix".equals(statisticDimension)) {
            result.setMatrixName("暂无数据");
            result.setEvaluateDepartName(null);
        } else {
            result.setMatrixName(null);
            result.setEvaluateDepartName("暂无数据");
        }

        // 设置默认值
        result.setRiskTotalCount(0);
        result.setControlTotalCount(0);
        result.setUnacceptableRiskCount(0);
        result.setUnacceptableRiskPercent(BigDecimal.ZERO);
        result.setIndicatorTotalCount(0);
        result.setYellowAlertIndicatorCount(0);
        result.setRedAlertIndicatorCount(0);
        result.setMonitoringTotalCount(0);
        result.setYellowAlertCount(0);
        result.setRedAlertCount(0);
        result.setEventTotalCount(0);
        result.setMajorEventCount(0);
        result.setNetLossAmount(BigDecimal.ZERO);
        result.setIssueTotalCount(0);
        result.setUnsolvedIssueCount(0);
        result.setUnsolvedIssuePercent(BigDecimal.ZERO);

        // 计算百分比和格式化
        calculatePercentages(result);
        formatAmountDisplay(result);

        return result;
    }

    /**
     * 处理统计结果
     */
    private void processStatisticsResult(RcsaMatrixStatisticsVo result) {
        // 设置默认值（防止null）
        if (result.getRiskTotalCount() == null) result.setRiskTotalCount(0);
        if (result.getControlTotalCount() == null) result.setControlTotalCount(0);
        if (result.getUnacceptableRiskCount() == null) result.setUnacceptableRiskCount(0);
        if (result.getIndicatorTotalCount() == null) result.setIndicatorTotalCount(0);
        if (result.getYellowAlertIndicatorCount() == null) result.setYellowAlertIndicatorCount(0);
        if (result.getRedAlertIndicatorCount() == null) result.setRedAlertIndicatorCount(0);
        if (result.getMonitoringTotalCount() == null) result.setMonitoringTotalCount(0);
        if (result.getYellowAlertCount() == null) result.setYellowAlertCount(0);
        if (result.getRedAlertCount() == null) result.setRedAlertCount(0);
        if (result.getEventTotalCount() == null) result.setEventTotalCount(0);
        if (result.getMajorEventCount() == null) result.setMajorEventCount(0);
        if (result.getNetLossAmount() == null) result.setNetLossAmount(BigDecimal.ZERO);

        // 设置问题信息模块默认值（预留）
        result.setIssueTotalCount(0);
        result.setUnsolvedIssueCount(0);
        result.setUnsolvedIssuePercent(BigDecimal.ZERO);

        // 计算百分比
        calculatePercentages(result);

        // 格式化金额显示
        formatAmountDisplay(result);
    }

    /**
     * 计算各项百分比
     */
    private void calculatePercentages(RcsaMatrixStatisticsVo result) {
        // 计算不可接受风险占比
        if (result.getRiskTotalCount() != null && result.getRiskTotalCount() > 0) {
            BigDecimal percent = BigDecimal.valueOf(result.getUnacceptableRiskCount() != null ? result.getUnacceptableRiskCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getRiskTotalCount()), 2, RoundingMode.HALF_UP);
            result.setUnacceptableRiskPercent(percent);
        } else {
            result.setUnacceptableRiskPercent(BigDecimal.ZERO);
        }

        // 计算指标预警占比
        if (result.getIndicatorTotalCount() != null && result.getIndicatorTotalCount() > 0) {
            // 黄色预警指标占比
            BigDecimal yellowIndicatorPercent = BigDecimal.valueOf(result.getYellowAlertIndicatorCount() != null ? result.getYellowAlertIndicatorCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getIndicatorTotalCount()), 2, RoundingMode.HALF_UP);
            result.setYellowAlertIndicatorPercent(yellowIndicatorPercent);

            // 红色预警指标占比
            BigDecimal redIndicatorPercent = BigDecimal.valueOf(result.getRedAlertIndicatorCount() != null ? result.getRedAlertIndicatorCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getIndicatorTotalCount()), 2, RoundingMode.HALF_UP);
            result.setRedAlertIndicatorPercent(redIndicatorPercent);
        } else {
            result.setYellowAlertIndicatorPercent(BigDecimal.ZERO);
            result.setRedAlertIndicatorPercent(BigDecimal.ZERO);
        }

        // 计算预警次数占比
        if (result.getMonitoringTotalCount() != null && result.getMonitoringTotalCount() > 0) {
            // 黄色预警次数占比
            BigDecimal yellowAlertPercent = BigDecimal.valueOf(result.getYellowAlertCount() != null ? result.getYellowAlertCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getMonitoringTotalCount()), 2, RoundingMode.HALF_UP);
            result.setYellowAlertPercent(yellowAlertPercent);

            // 红色预警次数占比
            BigDecimal redAlertPercent = BigDecimal.valueOf(result.getRedAlertCount() != null ? result.getRedAlertCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getMonitoringTotalCount()), 2, RoundingMode.HALF_UP);
            result.setRedAlertPercent(redAlertPercent);
        } else {
            result.setYellowAlertPercent(BigDecimal.ZERO);
            result.setRedAlertPercent(BigDecimal.ZERO);
        }

        // 计算重大事件占比
        if (result.getEventTotalCount() != null && result.getEventTotalCount() > 0) {
            BigDecimal majorEventPercent = BigDecimal.valueOf(result.getMajorEventCount() != null ? result.getMajorEventCount() : 0)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getEventTotalCount()), 2, RoundingMode.HALF_UP);
            result.setMajorEventPercent(majorEventPercent);
        } else {
            result.setMajorEventPercent(BigDecimal.ZERO);
        }
    }

    /**
     * 格式化金额显示
     */
    private void formatAmountDisplay(RcsaMatrixStatisticsVo result) {
        if (result.getNetLossAmount() != null) {
            DecimalFormat formatter = new DecimalFormat("#,##0.00");
            result.setNetLossAmountFormatted(formatter.format(result.getNetLossAmount()) + " 元");
        } else {
            result.setNetLossAmountFormatted("0.00 元");
        }
    }
}
