import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { h } from 'vue';

// 钻取类型常量定义
export const DRILL_DOWN_TYPES = {
  TOTAL_MATRIX_COUNT: 'totalMatrixCount',
  COMPLETED_MATRIX_COUNT: 'completedMatrixCount',
  COMPLETED_OVERDUE_MATRIX_COUNT: 'completedOverdueMatrixCount',
  UNCOMPLETED_MATRIX_COUNT: 'uncompletedMatrixCount',
  UNCOMPLETED_OVERDUE_MATRIX_COUNT: 'uncompletedOverdueMatrixCount',
} as const;

// 钻取类型颜色映射
export const DRILL_DOWN_COLORS = {
  [DRILL_DOWN_TYPES.TOTAL_MATRIX_COUNT]: '#1890ff',
  [DRILL_DOWN_TYPES.COMPLETED_MATRIX_COUNT]: '#52c41a',
  [DRILL_DOWN_TYPES.COMPLETED_OVERDUE_MATRIX_COUNT]: '#faad14',
  [DRILL_DOWN_TYPES.UNCOMPLETED_MATRIX_COUNT]: '#f5222d',
  [DRILL_DOWN_TYPES.UNCOMPLETED_OVERDUE_MATRIX_COUNT]: '#ff4d4f',
} as const;

// 钻取类型显示名称映射
export const DRILL_DOWN_DISPLAY_NAMES = {
  [DRILL_DOWN_TYPES.TOTAL_MATRIX_COUNT]: '计划评估矩阵总数',
  [DRILL_DOWN_TYPES.COMPLETED_MATRIX_COUNT]: '已完成评估矩阵数',
  [DRILL_DOWN_TYPES.COMPLETED_OVERDUE_MATRIX_COUNT]: '已完成但逾期的矩阵数',
  [DRILL_DOWN_TYPES.UNCOMPLETED_MATRIX_COUNT]: '未完成评估矩阵数',
  [DRILL_DOWN_TYPES.UNCOMPLETED_OVERDUE_MATRIX_COUNT]: '未完成且逾期的矩阵数',
} as const;

// 钻取类型简短显示名称映射（用于标签）
export const DRILL_DOWN_SHORT_NAMES = {
  [DRILL_DOWN_TYPES.TOTAL_MATRIX_COUNT]: '全部矩阵',
  [DRILL_DOWN_TYPES.COMPLETED_MATRIX_COUNT]: '已完成矩阵',
  [DRILL_DOWN_TYPES.COMPLETED_OVERDUE_MATRIX_COUNT]: '已完成但逾期矩阵',
  [DRILL_DOWN_TYPES.UNCOMPLETED_MATRIX_COUNT]: '未完成矩阵',
  [DRILL_DOWN_TYPES.UNCOMPLETED_OVERDUE_MATRIX_COUNT]: '未完成且逾期矩阵',
} as const;

// 可钻取的列数据索引
export const DRILLABLE_COLUMNS = [
  DRILL_DOWN_TYPES.TOTAL_MATRIX_COUNT,
  DRILL_DOWN_TYPES.COMPLETED_MATRIX_COUNT,
  DRILL_DOWN_TYPES.COMPLETED_OVERDUE_MATRIX_COUNT,
  DRILL_DOWN_TYPES.UNCOMPLETED_MATRIX_COUNT,
  DRILL_DOWN_TYPES.UNCOMPLETED_OVERDUE_MATRIX_COUNT,
] as const;

// 表格列定义（不包含钻取功能，钻取功能在组件中实现）
export const columns: BasicColumn[] = [
  {
    title: '评估部门',
    align: 'center',
    dataIndex: 'evaluateDepart_dictText',
    width: 150,
  },
  {
    title: '计划标题',
    align: 'center',
    dataIndex: 'planTitle',
    width: 200,
  },
  {
    title: '计划类型',
    align: 'center',
    dataIndex: 'planType_dictText',
    width: 120,
  },
  {
    title: '评估截止日期',
    align: 'center',
    dataIndex: 'evaluateEndDate',
    width: 120,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleDateString() : '';
    },
  },
  {
    title: '计划评估矩阵总数',
    align: 'center',
    dataIndex: 'totalMatrixCount',
    width: 140,
  },
  {
    title:'已完成评估矩阵',
    align: 'center',
    children: [
      {
        title: '已完成评估矩阵数',
        align: 'center',
        dataIndex: 'completedMatrixCount',
        width: 140,
      },
      {
        title: '其中已完成但逾期的矩阵数',
        align: 'center',
        dataIndex: 'completedOverdueMatrixCount',
        width: 180,
      }
    ]
  },
  {
    title: '未完成评估矩阵',
    align: 'center',
    children: [
      {
        title: '未完成评估矩阵数',
        align: 'center',
        dataIndex: 'uncompletedMatrixCount',
        width: 140,
      },
      {
        title: '其中未完成且逾期的矩阵数',
        align: 'center',
        dataIndex: 'uncompletedOverdueMatrixCount',
        width: 180,
      },
    ]
  }
];

// 明细数据列定义
export const detailColumns: BasicColumn[] = [
  // {
  //   title: '评估编号',
  //   align: 'center',
  //   dataIndex: 'taskCode',
  //   width: 120,
  // },
  {
    title: '评估部门',
    align: 'center',
    dataIndex: 'evaluateDepart_dictText',
    width: 150,
  },
  // {
  //   title: '计划标题',
  //   align: 'center',
  //   dataIndex: 'planTitle',
  //   width: 200,
  // },
  // {
  //   title: '计划类型',
  //   align: 'center',
  //   dataIndex: 'planType_dictText',
  //   width: 120,
  // },
  {
    title: '矩阵编号',
    align: 'center',
    dataIndex: 'matrixNum',
    width: 120,
  },
  {
    title: '矩阵名称',
    align: 'center',
    dataIndex: 'matrixName',
    width: 200,
  },
  {
    title: '评估截止日期',
    align: 'center',
    dataIndex: 'evaluateEndDate',
    width: 120,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleDateString() : '';
    },
  },
  {
    title: '实际完成日期',
    align: 'center',
    dataIndex: 'finishDate',
    width: 120,
    customRender: ({ text }) => {
      return text ? new Date(text).toLocaleDateString() : '';
    },
  },
  {
    title: '任务状态',
    align: 'center',
    dataIndex: 'taskState_dictText',
    width: 100,
  },
  {
    title: '是否逾期',
    align: 'center',
    dataIndex: 'isOverdue',
    width: 100,
    customRender: ({ text }) => {
      const color = text === '正常' ? '#52c41a' : '#f5222d';
      return h('span', { style: { color } }, text);
    },
  },
  // {
  //   title: '创建时间',
  //   align: 'center',
  //   dataIndex: 'createTime',
  //   width: 150,
  //   customRender: ({ text }) => {
  //     return text ? new Date(text).toLocaleString() : '';
  //   },
  // },
];

// 查询表单配置
export const searchFormSchema: FormSchema[] = [
  {
    label: '评估期间',
    field: 'dateRange',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
    },
    colProps: { span: 8 },
  },
  {
    label: '计划类型',
    field: 'planTypes',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rcsa_plan_type',
      mode: 'multiple',
      placeholder: '请选择计划类型',
    },
    colProps: { span: 8 },
  },
  {
    label: '评估部门',
    field: 'departIds',
    component: 'JSelectDept',
    componentProps: {
      multiple: true,
      placeholder: '请选择评估部门',
    },
    colProps: { span: 8 },
  },
];
