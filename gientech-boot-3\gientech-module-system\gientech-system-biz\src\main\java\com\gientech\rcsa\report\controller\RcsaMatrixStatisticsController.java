package com.gientech.rcsa.report.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.service.IRcsaMatrixStatisticsService;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.config.JeecgBaseConfig;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Controller
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Tag(name = "RCSA矩阵关联要素统计")
@RestController
@RequestMapping("/rcsa/report/matrix")
@Slf4j
public class RcsaMatrixStatisticsController {

    @Autowired
    private IRcsaMatrixStatisticsService rcsaMatrixStatisticsService;

    @Resource
    private JeecgBaseConfig jeecgBaseConfig;

    /**
     * 分页查询RCSA矩阵关联要素统计数据
     *
     * @param queryDto 查询条件
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param req 请求参数
     * @return 分页统计结果
     */
    @AutoLog(value = "RCSA矩阵关联要素统计-分页查询统计数据")
    @Operation(summary = "分页查询RCSA矩阵关联要素统计数据")
    @GetMapping(value = "/statistics")
    public Result<IPage<RcsaMatrixStatisticsVo>> queryMatrixStatistics(
            RcsaMatrixStatisticsDto queryDto,
            @org.springframework.web.bind.annotation.RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @org.springframework.web.bind.annotation.RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
            HttpServletRequest req) {
        try {
            log.info("分页查询RCSA矩阵关联要素统计数据，查询条件：{}，页码：{}，页大小：{}", queryDto, pageNo, pageSize);

            Page<RcsaMatrixStatisticsVo> page = new Page<>(pageNo, pageSize);
            IPage<RcsaMatrixStatisticsVo> result = rcsaMatrixStatisticsService.queryMatrixStatistics(page, queryDto);

            log.info("RCSA矩阵关联要素统计分页查询成功，返回{}条记录", result.getRecords().size());
            return Result.OK(result);

        } catch (Exception e) {
            log.error("分页查询RCSA矩阵关联要素统计数据失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出Excel
     *
     * @param queryDto 查询条件
     * @param req 请求参数
     * @param response 响应参数
     * @return ModelAndView
     */
    @AutoLog(value = "RCSA矩阵关联要素统计-导出Excel")
    @Operation(summary = "导出RCSA矩阵关联要素统计Excel")
    @GetMapping(value = "/statistics/exportXls")
    public ModelAndView exportXls(
            RcsaMatrixStatisticsDto queryDto,
            HttpServletRequest req,
            HttpServletResponse response) {
        log.info("导出RCSA矩阵关联要素统计Excel，查询条件：{}", queryDto);
        List<RcsaMatrixStatisticsVo> exportList = rcsaMatrixStatisticsService.queryAllMatrixStatistics(queryDto);
        // 设置导出文件名
        String fileName = generateExportFileName();
        // 设置导出标题
        String title = generateExportTitle();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // Step.3 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        //此处设置的filename无效 ,前端会重更新设置一下
        mv.addObject(NormalExcelConstants.FILE_NAME, title);
        mv.addObject(NormalExcelConstants.CLASS, RcsaMatrixStatisticsVo.class);
        //update-begin--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置--------------------
        ExportParams exportParams = new ExportParams(title + "报表", "导出人:" + sysUser.getRealname(), title);
        exportParams.setImageBasePath(jeecgBaseConfig.getPath().getUpload());
        //update-end--Author:liusq  Date:20210126 for：图片导出报错，ImageBasePath未设置----------------------
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 生成导出文件名
     */
    private String generateExportFileName() {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        return "RCSA矩阵关联要素统计_" + now.format(formatter);
    }

    /**
     * 生成导出标题
     */
    private String generateExportTitle() {
        java.time.LocalDate now = java.time.LocalDate.now();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        return now.format(formatter) + " RCSA矩阵关联要素统计";
    }
}
