<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :title="tableTitle">
      <template #toolbar>
        <a-button type="primary" preIcon="ant-design:download-outlined" @click="onExportXls"> 导出</a-button>
      </template>
      <!--操作栏-->
      <template #headerTop>
        <BasicForm @register="registerForm" @submit="handleSubmit" @reset="handleReset" />
      </template>
      <!--自定义渲染-->
      <template #bodyCell="{ column, record }">
        <template v-if="isDrillableColumn(column.dataIndex)">
          <template v-if="getDrillableCount(record, column.dataIndex) > 0">
            <a @click="handleDrillDown(record, column.dataIndex)" style="color: #1890ff; cursor: pointer">{{
              getFormattedText(record, column.dataIndex)
            }}</a>
          </template>
          <template v-else>
            <span>{{ getFormattedText(record, column.dataIndex) }}</span>
          </template>
        </template>
      </template>
    </BasicTable>

    <!-- 风险钻取详情弹窗 -->
    <RiskDetailModal @register="registerRiskDetailModal" @success="handleSuccess" />

    <!-- 控制钻取详情弹窗 -->
    <ControlDetailModal @register="registerControlDetailModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
  import { reactive, computed } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import RiskDetailModal from './components/RiskDetailModal.vue';
  import ControlDetailModal from './components/ControlDetailModal.vue';
  import { summaryColumns, searchFormSchema, DRILLABLE_COLUMNS, DRILL_TYPE_MAP } from './RcsaAssessmentResultSummary.data';
  import { queryAssessmentResultSummary, getExportUrl } from './RcsaAssessmentResultSummary.api';

  const queryParam = reactive<any>({
    column: 'riskTotalCount',
    order: 'desc',
  });
  const [registerRiskDetailModal, { openModal: openRiskDetailModal }] = useModal();
  const [registerControlDetailModal, { openModal: openControlDetailModal }] = useModal();

  // 表格标题 - 当前日期
  const tableTitle = computed(() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}年${month}月${day}日 RCSA评估结果汇总表`;
  });

  // 列配置 - 根据统计维度动态调整
  const dynamicColumns = computed(() => {
    const cols = [...summaryColumns];
    const statisticDimension = queryParam.statisticDimension || 'department';

    // 根据统计维度显示/隐藏列
    cols.forEach((col) => {
      if (col.dataIndex === 'matrixName') {
        col.ifShow = statisticDimension === 'matrix';
      }
      if (col.dataIndex === 'matrixCount') {
        col.ifShow = statisticDimension === 'department';
      }
    });

    return cols;
  });

  //注册table数据
  const { tableContext, onExportXls } = useListPage({
    tableProps: {
      api: queryAssessmentResultSummary,
      columns: dynamicColumns,
      canResize: false,
      useSearchForm: false,
      showActionColumn: false,
      defSort: {
        column: 'riskTotalCount',
        order: 'desc',
      },
      tableSetting: {
        redo: false,
        size: false,
        setting: false,
      },
      beforeFetch: async (params) => {
        let rangerQuery = await setRangeQuery();
        return Object.assign(params, rangerQuery);
      },
    },
    exportConfig: {
      name: 'RCSA评估结果汇总表',
      url: getExportUrl,
      params: queryParam,
    },
  });

  const [registerTable, { reload }] = tableContext;

  // 表单配置
  const [registerForm, { getFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
    showAdvancedButton: false,
    fieldMapToNumber: [],
    fieldMapToTime: [['dateRange', ['evaluateStartDate', 'evaluateEndDate'], 'YYYY-MM-DD']],
    autoAdvancedLine: 3,
    actionColOptions: {
      span: 8,
    },
  });

  /**
   * 查询
   */
  function handleSubmit() {
    const values = getFieldsValue();
    Object.assign(queryParam, values);
    reload();
  }

  /**
   * 重置
   */
  function handleReset() {
    Object.keys(queryParam).forEach((key) => {
      delete queryParam[key];
    });
    reload();
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 判断是否为可钻取的列
   */
  function isDrillableColumn(dataIndex: string) {
    return DRILLABLE_COLUMNS.includes(dataIndex);
  }

  /**
   * 获取可钻取列的数量值
   */
  function getDrillableCount(record: any, dataIndex: string) {
    return record[dataIndex] || 0;
  }

  /**
   * 获取格式化的显示文本（数量 + 百分比）
   */
  function getFormattedText(record: any, dataIndex: string) {
    const count = record[dataIndex] || 0;

    // 风险总数和控制总数只显示数量，不显示百分比
    if (dataIndex === 'riskTotalCount' || dataIndex === 'controlTotalCount') {
      return count.toString();
    }

    // 根据dataIndex获取对应的百分比字段
    const percentField = dataIndex.replace('Count', 'Percent');
    const percent = record[percentField] || 0;

    return `${count} (${percent}%)`;
  }

  /**
   * 处理钻取点击
   */
  function handleDrillDown(record: any, dataIndex: string) {
    const drillConfig = DRILL_TYPE_MAP[dataIndex];
    if (!drillConfig) return;

    const { type: drillType, level: drillLevel } = drillConfig;

    // 构建钻取查询条件，继承主查询的筛选条件
    const drillQueryParam = { ...queryParam };

    // 添加统计维度参数（重要：确保钻取查询与主查询维度一致）
    const statisticDimension = queryParam.statisticDimension || 'department';
    drillQueryParam.statisticDimension = statisticDimension;

    // 添加当前行的特定条件（重要：根据当前行数据进行过滤）
    if (record.evaluateDepart) {
      drillQueryParam.evaluateDepart = record.evaluateDepart;
    }

    // 根据统计维度添加矩阵条件
    if (statisticDimension === 'matrix' && record.matrixName) {
      drillQueryParam.matrixName = record.matrixName;
    }

    // 根据钻取类型添加特定的过滤条件
    if (drillType === 'riskTotal' || drillType.includes('Risk') || drillType === 'unacceptableRemainRisk') {
      // 风险相关钻取
      if (drillType === 'riskTotal') {
        // 风险总数钻取，不添加额外的风险级别过滤条件
      } else if (drillType === 'inherentRisk') {
        drillQueryParam.inherentRiskLevel = drillLevel;
      } else if (drillType === 'adjustedRemainRisk') {
        drillQueryParam.adjustedRemainRiskLevel = drillLevel;
      } else if (drillType === 'unacceptableRemainRisk') {
        drillQueryParam.remainRiskIsAccept = '0'; // 不接受
      }

      console.log('风险钻取参数:', {
        record,
        drillType,
        drillLevel,
        queryParam: drillQueryParam,
      });

      openRiskDetailModal(true, {
        record,
        queryParam: drillQueryParam,
        drillType,
        drillLevel,
      });
    } else if (drillType === 'controlTotal' || drillType.includes('control')) {
      // 控制相关钻取
      if (drillType === 'controlTotal') {
        // 控制总数钻取，不添加额外的控制级别过滤条件
      } else if (drillType === 'controlDesign') {
        drillQueryParam.controlDesignRationality = drillLevel;
      } else if (drillType === 'controlExecute') {
        drillQueryParam.controlExecuteAvailability = drillLevel;
      } else if (drillType === 'controlRating') {
        drillQueryParam.controlRating = drillLevel;
      }

      console.log('控制钻取参数:', {
        record,
        drillType,
        drillLevel,
        queryParam: drillQueryParam,
      });

      openControlDetailModal(true, {
        record,
        queryParam: drillQueryParam,
        drillType,
        drillLevel,
      });
    }
  }

  /**
   * 设置时间查询条件
   */
  async function setRangeQuery() {
    let rangeQuery = {};
    const values = getFieldsValue();

    // 处理日期范围
    if (values.evaluateStartDate && values.evaluateEndDate) {
      rangeQuery['evaluateStartDate'] = values.evaluateStartDate;
      rangeQuery['evaluateEndDate'] = values.evaluateEndDate;
    }

    // 处理统计维度
    if (values.statisticDimension) {
      rangeQuery['statisticDimension'] = values.statisticDimension;
    }

    // 处理计划类型
    if (values.planTypes && values.planTypes.length > 0) {
      rangeQuery['planType'] = values.planTypes;
    }

    // 处理评估部门
    if (values.evaluateDepart && values.evaluateDepart.length > 0) {
      rangeQuery['evaluateDepart'] = values.evaluateDepart;
    }

    return rangeQuery;
  }
</script>

<style scoped></style>
