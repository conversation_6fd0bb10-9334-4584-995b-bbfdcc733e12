package com.gientech.rcsa.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gientech.rcsa.report.dto.RcsaMatrixStatisticsDto;
import com.gientech.rcsa.report.vo.RcsaMatrixStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: RCSA矩阵关联要素统计Mapper
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Mapper
public interface RcsaMatrixStatisticsMapper {

    /**
     * 分页查询RCSA矩阵关联要素统计数据
     *
     * @param page 分页参数
     * @param queryDto 查询条件
     * @return 分页统计结果
     */
    IPage<RcsaMatrixStatisticsVo> queryMatrixStatistics(Page<RcsaMatrixStatisticsVo> page, @Param("queryDto") RcsaMatrixStatisticsDto queryDto);

    /**
     * 查询所有RCSA矩阵关联要素统计数据（用于导出）
     *
     * @param queryDto 查询条件
     * @return 统计结果列表
     */
    List<RcsaMatrixStatisticsVo> queryAllMatrixStatistics(@Param("queryDto") RcsaMatrixStatisticsDto queryDto);
}
