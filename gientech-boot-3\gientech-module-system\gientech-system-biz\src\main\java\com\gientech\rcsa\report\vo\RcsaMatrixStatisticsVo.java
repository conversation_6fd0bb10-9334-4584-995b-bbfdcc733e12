package com.gientech.rcsa.report.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * @Description: RCSA矩阵关联要素统计结果VO
 * @Author: jeecg-boot
 * @Date: 2025-08-21
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "RCSA矩阵关联要素统计结果VO")
public class RcsaMatrixStatisticsVo {

    // ========== 基础信息模块 ==========
    /**
     * 矩阵名称
     */
    @Excel(name = "矩阵名称", width = 20, orderNum = "1")
    @Schema(description = "矩阵名称")
    private String matrixName;

    /**
     * 评估机构名称
     */
    @Excel(name = "评估机构名称", width = 20, orderNum = "2")
    @Schema(description = "评估机构名称")
    private String evaluateDepartName;

    /**
     * 风险总数量
     */
    @Excel(name = "风险总数量", width = 15, orderNum = "3")
    @Schema(description = "风险总数量")
    private Integer riskTotalCount;

    /**
     * 控制总数量
     */
    @Excel(name = "控制总数量", width = 15, orderNum = "4")
    @Schema(description = "控制总数量")
    private Integer controlTotalCount;

    /**
     * 不可接受风险数量
     */
    @Excel(name = "不可接受风险数量", width = 18, orderNum = "5")
    @Schema(description = "不可接受风险数量")
    private Integer unacceptableRiskCount;

    /**
     * 不可接受风险占比
     */
    @Excel(name = "不可接受风险占比(%)", width = 18, orderNum = "6")
    @Schema(description = "不可接受风险占比")
    private BigDecimal unacceptableRiskPercent;

    // ========== 指标预警模块 ==========
    /**
     * 关联指标总数量
     */
    @Excel(name = "关联指标总数量", width = 18, orderNum = "7")
    @Schema(description = "关联指标总数量")
    private Integer indicatorTotalCount;

    /**
     * 黄色预警指标数量
     */
    @Excel(name = "黄色预警指标数量", width = 18, orderNum = "8")
    @Schema(description = "黄色预警指标数量")
    private Integer yellowAlertIndicatorCount;

    /**
     * 黄色预警指标占比
     */
    @Excel(name = "黄色预警指标占比(%)", width = 20, orderNum = "9")
    @Schema(description = "黄色预警指标占比")
    private BigDecimal yellowAlertIndicatorPercent;

    /**
     * 红色预警指标数量
     */
    @Excel(name = "红色预警指标数量", width = 18, orderNum = "10")
    @Schema(description = "红色预警指标数量")
    private Integer redAlertIndicatorCount;

    /**
     * 红色预警指标占比
     */
    @Excel(name = "红色预警指标占比(%)", width = 20, orderNum = "11")
    @Schema(description = "红色预警指标占比")
    private BigDecimal redAlertIndicatorPercent;

    /**
     * 监测总次数
     */
    @Excel(name = "监测总次数", width = 15, orderNum = "12")
    @Schema(description = "监测总次数")
    private Integer monitoringTotalCount;

    /**
     * 黄色预警次数
     */
    @Excel(name = "黄色预警次数", width = 15, orderNum = "13")
    @Schema(description = "黄色预警次数")
    private Integer yellowAlertCount;

    /**
     * 黄色预警次数占比
     */
    @Excel(name = "黄色预警次数占比(%)", width = 20, orderNum = "14")
    @Schema(description = "黄色预警次数占比")
    private BigDecimal yellowAlertPercent;

    /**
     * 红色预警次数
     */
    @Excel(name = "红色预警次数", width = 15, orderNum = "15")
    @Schema(description = "红色预警次数")
    private Integer redAlertCount;

    /**
     * 红色预警次数占比
     */
    @Excel(name = "红色预警次数占比(%)", width = 20, orderNum = "16")
    @Schema(description = "红色预警次数占比")
    private BigDecimal redAlertPercent;

    // ========== 损失事件模块 ==========
    /**
     * 事件总数量
     */
    @Excel(name = "事件总数量", width = 15, orderNum = "17")
    @Schema(description = "事件总数量")
    private Integer eventTotalCount;

    /**
     * 重大事件数量
     */
    @Excel(name = "重大事件数量", width = 15, orderNum = "18")
    @Schema(description = "重大事件数量")
    private Integer majorEventCount;

    /**
     * 重大事件占比
     */
    @Excel(name = "重大事件占比(%)", width = 18, orderNum = "19")
    @Schema(description = "重大事件占比")
    private BigDecimal majorEventPercent;

    /**
     * 净损失金额
     */
    @Schema(description = "净损失金额")
    private BigDecimal netLossAmount;

    /**
     * 净损失金额格式化显示
     */
    @Excel(name = "净损失金额", width = 20, orderNum = "20")
    @Schema(description = "净损失金额格式化显示")
    private String netLossAmountFormatted;

    // ========== 问题信息模块（预留） ==========
    /**
     * 问题总数量（预留）
     */
    @Schema(description = "问题总数量（预留）")
    private Integer issueTotalCount;

    /**
     * 未解决问题数量（预留）
     */
    @Schema(description = "未解决问题数量（预留）")
    private Integer unsolvedIssueCount;

    /**
     * 未解决问题占比（预留）
     */
    @Schema(description = "未解决问题占比（预留）")
    private BigDecimal unsolvedIssuePercent;
}
